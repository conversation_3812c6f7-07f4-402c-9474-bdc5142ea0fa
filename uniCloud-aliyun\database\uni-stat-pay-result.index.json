[{"IndexName": "appid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "appid", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "platform_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "platform_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "channel_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "channel_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "version_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "version_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "dimension", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "dimension", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "create_date", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "create_date", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "start_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "start_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "end_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "end_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "stat_date.date_str", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "stat_date.date_str", "Direction": "1"}], "MgoIsUnique": false}}]