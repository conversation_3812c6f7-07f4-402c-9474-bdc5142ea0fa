<template>
  <view class="admin-dashboard">
    <!-- 概览卡片 -->
    <uni-row :gutter="20" class="overview-section">
      <uni-col :span="6">
        <uni-card title="总用户数" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ dashboardData.overview?.totalUsers || 0 }}</view>
            <view class="stat-label">
              <text>今日新增: {{ dashboardData.today?.newUsers || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="总房源数" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ dashboardData.overview?.totalHouses || 0 }}</view>
            <view class="stat-label">
              <text>今日新增: {{ dashboardData.today?.newHouses || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="总预约数" is-full>
          <view class="stat-card">
            <view class="stat-number">{{ dashboardData.overview?.totalAppointments || 0 }}</view>
            <view class="stat-label">
              <text>今日新增: {{ dashboardData.today?.newAppointments || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="6">
        <uni-card title="待处理举报" is-full>
          <view class="stat-card">
            <view class="stat-number urgent">{{ dashboardData.overview?.pendingReports || 0 }}</view>
            <view class="stat-label">
              <text>需要处理</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>

    <!-- 图表区域 -->
    <uni-row :gutter="20" class="chart-section">
      <uni-col :span="12">
        <uni-card title="用户注册趋势" is-full>
          <view class="chart-container">
            <canvas 
              canvas-id="userTrendChart" 
              class="chart-canvas"
              @touchstart="touchChart"
              @touchmove="moveChart"
              @touchend="touchEndChart"
            />
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="12">
        <uni-card title="快速操作" is-full>
          <view class="quick-actions">
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/user-management')"
            >
              <uni-icons type="person" size="20" />
              <text>用户管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/house-management')"
            >
              <uni-icons type="home" size="20" />
              <text>房源管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/appointment-management')"
            >
              <uni-icons type="calendar" size="20" />
              <text>预约管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/message-management')"
            >
              <uni-icons type="chat" size="20" />
              <text>消息管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/report-management')"
            >
              <uni-icons type="flag" size="20" />
              <text>举报管理</text>
            </button>
            <button 
              class="action-btn" 
              type="primary"
              @click="navigateTo('/pages/admin/system-config')"
            >
              <uni-icons type="gear" size="20" />
              <text>系统配置</text>
            </button>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>

    <!-- 统计详情 -->
    <uni-row :gutter="20" class="detail-section">
      <uni-col :span="8">
        <uni-card title="用户统计" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">学生用户:</text>
              <text class="value">{{ userStats.students || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">房东用户:</text>
              <text class="value">{{ userStats.landlords || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">活跃用户:</text>
              <text class="value">{{ userStats.active || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">本月新增:</text>
              <text class="value">{{ userStats.monthNew || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="8">
        <uni-card title="房源统计" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">已审核:</text>
              <text class="value">{{ houseStats.verified || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">待审核:</text>
              <text class="value">{{ houseStats.pending || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">可租房源:</text>
              <text class="value">{{ houseStats.available || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">已租房源:</text>
              <text class="value">{{ houseStats.rented || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
      <uni-col :span="8">
        <uni-card title="预约统计" is-full>
          <view class="detail-stats">
            <view class="stat-item">
              <text class="label">待确认:</text>
              <text class="value">{{ appointmentStats.pending || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">已确认:</text>
              <text class="value">{{ appointmentStats.confirmed || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">已完成:</text>
              <text class="value">{{ appointmentStats.completed || 0 }}</text>
            </view>
            <view class="stat-item">
              <text class="label">今日预约:</text>
              <text class="value">{{ appointmentStats.todayNew || 0 }}</text>
            </view>
          </view>
        </uni-card>
      </uni-col>
    </uni-row>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      dashboardData: {},
      userStats: {},
      houseStats: {},
      appointmentStats: {},
      chart: null
    }
  },
  
  onLoad() {
    this.loadDashboardData();
    this.loadStatistics();
  },
  
  onReady() {
    this.initChart();
  },
  
  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'getDashboardData'
          }
        });
        
        if (res.result.code === 0) {
          this.dashboardData = res.result.data;
          this.updateChart();
        }
      } catch (error) {
        console.error('加载仪表板数据失败:', error);
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const [userRes, houseRes, appointmentRes] = await Promise.all([
          uniCloud.callFunction({
            name: 'admin-management',
            data: { action: 'getUserStatistics' }
          }),
          uniCloud.callFunction({
            name: 'admin-management',
            data: { action: 'getHouseStatistics' }
          }),
          uniCloud.callFunction({
            name: 'admin-management',
            data: { action: 'getAppointmentStatistics' }
          })
        ]);
        
        if (userRes.result.code === 0) {
          this.userStats = userRes.result.data;
        }
        if (houseRes.result.code === 0) {
          this.houseStats = houseRes.result.data;
        }
        if (appointmentRes.result.code === 0) {
          this.appointmentStats = appointmentRes.result.data;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },
    
    // 初始化图表
    initChart() {
      // 这里可以使用 uCharts 或其他图表库
      // 简化示例，实际项目中需要引入图表库
      const ctx = uni.createCanvasContext('userTrendChart', this);
      this.chart = ctx;
      this.updateChart();
    },
    
    // 更新图表
    updateChart() {
      if (!this.chart || !this.dashboardData.trend) return;
      
      // 简化的图表绘制逻辑
      // 实际项目中应该使用专业的图表库
      const ctx = this.chart;
      ctx.clearRect(0, 0, 300, 200);
      
      // 绘制简单的折线图
      const data = this.dashboardData.trend.userRegistration || [];
      if (data.length > 0) {
        ctx.beginPath();
        ctx.setStrokeStyle('#007aff');
        ctx.setLineWidth(2);
        
        data.forEach((item, index) => {
          const x = (index / (data.length - 1)) * 280 + 10;
          const y = 180 - (item.count / Math.max(...data.map(d => d.count))) * 160;
          
          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        
        ctx.stroke();
      }
      
      ctx.draw();
    },
    
    // 导航到指定页面
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    
    // 图表触摸事件
    touchChart(e) {
      // 图表交互逻辑
    },
    
    moveChart(e) {
      // 图表移动逻辑
    },
    
    touchEndChart(e) {
      // 图表触摸结束逻辑
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
}

.overview-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10px;
}

.stat-number.urgent {
  color: #ff3b30;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-canvas {
  width: 100%;
  height: 200px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 20px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  border: none;
  background-color: #007aff;
  color: white;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-stats {
  padding: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.stat-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}
</style>
