'use strict';

const uniCloud = require('uni-cloud');
const uniID = require('uni-id-common');

/**
 * 管理平台云函数
 * 提供后端管理所需的所有API接口
 */
exports.main = async (event, context) => {
  console.log('管理平台云函数被调用:', event);
  
  const { action, data } = event;
  
  if (!action) {
    return {
      code: 400,
      message: '缺少action参数'
    };
  }
  
  try {
    switch (action) {
      // 用户管理相关
      case 'getUserList':
        return await getUserList(event, context);
      case 'updateUserStatus':
        return await updateUserStatus(event, context);
      case 'batchUpdateUsers':
        return await batchUpdateUsers(event, context);
      case 'getUserStatistics':
        return await getUserStatistics(event, context);
      case 'verifyUserIdentity':
        return await verifyUserIdentity(event, context);
        
      // 房源管理相关
      case 'getHouseList':
        return await getHouseList(event, context);
      case 'auditHouse':
        return await auditHouse(event, context);
      case 'batchAuditHouses':
        return await batchAuditHouses(event, context);
      case 'getHouseStatistics':
        return await getHouseStatistics(event, context);
      case 'deleteHouse':
        return await deleteHouse(event, context);
        
      // 预约管理相关
      case 'getAppointmentList':
        return await getAppointmentList(event, context);
      case 'getAppointmentStatistics':
        return await getAppointmentStatistics(event, context);
        
      // 消息管理相关
      case 'getMessageList':
        return await getMessageList(event, context);
      case 'sendSystemMessage':
        return await sendSystemMessage(event, context);
      case 'batchSendMessage':
        return await batchSendMessage(event, context);
        
      // 举报管理相关
      case 'getReportList':
        return await getReportList(event, context);
      case 'handleReport':
        return await handleReport(event, context);
        
      // 统计分析相关
      case 'getDashboardData':
        return await getDashboardData(event, context);
      case 'getDetailedStatistics':
        return await getDetailedStatistics(event, context);
        
      // 系统配置相关
      case 'getSystemConfig':
        return await getSystemConfig(event, context);
      case 'updateSystemConfig':
        return await updateSystemConfig(event, context);
        
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('管理平台云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 权限验证中间件
async function checkAdminPermission(token, context, requiredPermission = null) {
  if (!token) {
    return {
      code: 401,
      message: '未提供认证token'
    };
  }
  
  try {
    const uniIdIns = uniID.createInstance({
      context: context
    });
    
    const checkTokenResult = await uniIdIns.checkToken(token);
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 401,
        message: 'token无效或已过期'
      };
    }
    
    const { uid } = checkTokenResult;
    
    // 获取用户信息
    const db = uniCloud.database();
    const userRes = await db.collection('uni-id-users').doc(uid).get();
    
    if (userRes.data.length === 0) {
      return {
        code: 401,
        message: '用户不存在'
      };
    }
    
    const user = userRes.data[0];
    
    // 检查是否为管理员
    if (!user.role || !user.role.includes('admin')) {
      return {
        code: 403,
        message: '权限不足，需要管理员权限'
      };
    }
    
    // 如果指定了特定权限，进行权限检查
    if (requiredPermission && user.permission && !user.permission.includes(requiredPermission)) {
      return {
        code: 403,
        message: `权限不足，需要${requiredPermission}权限`
      };
    }
    
    return {
      code: 0,
      uid: uid,
      user: user
    };
  } catch (error) {
    return {
      code: 500,
      message: '权限验证失败',
      error: error.message
    };
  }
}

// 用户管理相关函数
async function getUserList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }
  
  const { page = 1, pageSize = 20, keyword = '', status = null, role = null } = event.data || {};
  
  try {
    const db = uniCloud.database();
    let where = {};
    
    // 关键词搜索
    if (keyword) {
      where = db.command.or([
        { username: new RegExp(keyword, 'i') },
        { nickname: new RegExp(keyword, 'i') },
        { mobile: new RegExp(keyword, 'i') },
        { email: new RegExp(keyword, 'i') }
      ]);
    }
    
    // 状态筛选
    if (status !== null) {
      where.status = status;
    }
    
    // 角色筛选
    if (role) {
      where.role = db.command.in([role]);
    }
    
    // 获取总数
    const countResult = await db.collection('uni-id-users').where(where).count();
    const total = countResult.total;
    
    // 获取用户列表
    const userResult = await db.collection('uni-id-users')
      .where(where)
      .field({
        password: false,
        token: false
      })
      .orderBy('register_date', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: userResult.data,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取用户列表失败',
      error: error.message
    };
  }
}

// 更新用户状态
async function updateUserStatus(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }
  
  const { userId, status, reason = '' } = event.data;
  
  if (!userId || status === undefined) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }
  
  try {
    const db = uniCloud.database();
    
    // 更新用户状态
    await db.collection('uni-id-users').doc(userId).update({
      status: status,
      update_date: new Date()
    });
    
    // 记录操作日志
    await db.collection('admin_logs').add({
      admin_id: adminCheck.uid,
      action: 'update_user_status',
      target_id: userId,
      details: {
        status: status,
        reason: reason
      },
      create_date: new Date()
    });
    
    return {
      code: 0,
      message: '用户状态更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '更新用户状态失败',
      error: error.message
    };
  }
}

// 批量更新用户
async function batchUpdateUsers(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }
  
  const { userIds, operation, data } = event.data;
  
  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return {
      code: 400,
      message: '用户ID列表不能为空'
    };
  }
  
  try {
    const db = uniCloud.database();
    const batch = db.batch();
    
    switch (operation) {
      case 'updateStatus':
        userIds.forEach(userId => {
          batch.collection('uni-id-users').doc(userId).update({
            status: data.status,
            update_date: new Date()
          });
        });
        break;
        
      case 'addRole':
        userIds.forEach(userId => {
          batch.collection('uni-id-users').doc(userId).update({
            role: db.command.addToSet(data.role),
            update_date: new Date()
          });
        });
        break;
        
      case 'removeRole':
        userIds.forEach(userId => {
          batch.collection('uni-id-users').doc(userId).update({
            role: db.command.pull(data.role),
            update_date: new Date()
          });
        });
        break;
        
      default:
        return {
          code: 400,
          message: '无效的批量操作类型'
        };
    }
    
    await batch.commit();
    
    // 记录操作日志
    await db.collection('admin_logs').add({
      admin_id: adminCheck.uid,
      action: `batch_${operation}`,
      target_ids: userIds,
      details: data,
      create_date: new Date()
    });
    
    return {
      code: 0,
      message: `批量操作成功，影响${userIds.length}个用户`
    };
  } catch (error) {
    return {
      code: 500,
      message: '批量操作失败',
      error: error.message
    };
  }
}

// 获取用户统计信息
async function getUserStatistics(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();

    // 总用户数
    const totalUsers = await db.collection('uni-id-users').count();

    // 学生用户数
    const studentUsers = await db.collection('uni-id-users').where({
      'student_info.verified': true
    }).count();

    // 房东用户数
    const landlordUsers = await db.collection('uni-id-users').where({
      'landlord_info.verified': true
    }).count();

    // 今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsers = await db.collection('uni-id-users').where({
      register_date: db.command.gte(today)
    }).count();

    // 本月新增用户
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthUsers = await db.collection('uni-id-users').where({
      register_date: db.command.gte(monthStart)
    }).count();

    // 活跃用户（最近7天登录）
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const activeUsers = await db.collection('uni-id-users').where({
      last_login_date: db.command.gte(weekAgo)
    }).count();

    // 用户状态分布
    const statusStats = await db.collection('uni-id-users').aggregate()
      .group({
        _id: '$status',
        count: db.command.aggregate.sum(1)
      })
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalUsers.total,
        students: studentUsers.total,
        landlords: landlordUsers.total,
        todayNew: todayUsers.total,
        monthNew: monthUsers.total,
        active: activeUsers.total,
        statusDistribution: statusStats.data
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取用户统计失败',
      error: error.message
    };
  }
}

// 身份认证审核
async function verifyUserIdentity(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { userId, identityType, approved, reason = '' } = event.data;

  if (!userId || !identityType || approved === undefined) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }

  try {
    const db = uniCloud.database();

    let updateData = {};
    if (identityType === 'student') {
      updateData['student_info.verified'] = approved;
      updateData['student_info.verify_date'] = new Date();
      updateData['student_info.verify_reason'] = reason;
    } else if (identityType === 'landlord') {
      updateData['landlord_info.verified'] = approved;
      updateData['landlord_info.verify_date'] = new Date();
      updateData['landlord_info.verify_reason'] = reason;
    }

    await db.collection('uni-id-users').doc(userId).update(updateData);

    // 发送通知消息
    await db.collection('messages').add({
      from_user_id: 'system',
      to_user_id: userId,
      type: 'system',
      title: `${identityType === 'student' ? '学生' : '房东'}身份认证${approved ? '通过' : '未通过'}`,
      content: approved ? '恭喜您，身份认证已通过！' : `身份认证未通过，原因：${reason}`,
      is_read: false,
      create_date: new Date()
    });

    return {
      code: 0,
      message: '身份认证处理成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '身份认证处理失败',
      error: error.message
    };
  }
}

// 房源管理相关函数
async function getHouseList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 20, keyword = '', status = null, isVerified = null } = event.data || {};

  try {
    const db = uniCloud.database();
    let where = {};

    // 关键词搜索
    if (keyword) {
      where = db.command.or([
        { title: new RegExp(keyword, 'i') },
        { 'location.address': new RegExp(keyword, 'i') }
      ]);
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 审核状态筛选
    if (isVerified !== null) {
      where.is_verified = isVerified;
    }

    // 获取总数
    const countResult = await db.collection('houses').where(where).count();
    const total = countResult.total;

    // 获取房源列表，关联房东信息
    const houseResult = await db.collection('houses')
      .aggregate()
      .match(where)
      .lookup({
        from: 'uni-id-users',
        localField: 'landlord_id',
        foreignField: '_id',
        as: 'landlord'
      })
      .project({
        title: 1,
        type: 1,
        price: 1,
        area: 1,
        location: 1,
        status: 1,
        is_verified: 1,
        view_count: 1,
        favorite_count: 1,
        publish_date: 1,
        update_date: 1,
        'landlord.nickname': 1,
        'landlord.mobile': 1
      })
      .sort({ publish_date: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: houseResult.data,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取房源列表失败',
      error: error.message
    };
  }
}

// 房源审核
async function auditHouse(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { houseId, approved, reason = '' } = event.data;

  if (!houseId || approved === undefined) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }

  try {
    const db = uniCloud.database();

    // 获取房源信息
    const houseRes = await db.collection('houses').doc(houseId).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }

    const house = houseRes.data[0];

    // 更新审核状态
    await db.collection('houses').doc(houseId).update({
      is_verified: approved,
      audit_date: new Date(),
      audit_reason: reason,
      auditor_id: adminCheck.uid
    });

    // 发送通知给房东
    await db.collection('messages').add({
      from_user_id: 'system',
      to_user_id: house.landlord_id,
      type: 'system',
      title: `房源审核${approved ? '通过' : '未通过'}`,
      content: approved ?
        `您的房源"${house.title}"已通过审核，现在可以正常展示了！` :
        `您的房源"${house.title}"审核未通过，原因：${reason}`,
      related_id: houseId,
      is_read: false,
      create_date: new Date()
    });

    return {
      code: 0,
      message: '房源审核处理成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '房源审核处理失败',
      error: error.message
    };
  }
}

// 批量房源审核
async function batchAuditHouses(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { houseIds, approved, reason = '' } = event.data;

  if (!houseIds || !Array.isArray(houseIds) || houseIds.length === 0) {
    return {
      code: 400,
      message: '房源ID列表不能为空'
    };
  }

  try {
    const db = uniCloud.database();

    // 获取房源信息
    const housesRes = await db.collection('houses').where({
      _id: db.command.in(houseIds)
    }).get();

    // 批量更新审核状态
    const batch = db.batch();
    houseIds.forEach(houseId => {
      batch.collection('houses').doc(houseId).update({
        is_verified: approved,
        audit_date: new Date(),
        audit_reason: reason,
        auditor_id: adminCheck.uid
      });
    });

    await batch.commit();

    // 批量发送通知
    const messageBatch = db.batch();
    housesRes.data.forEach(house => {
      messageBatch.collection('messages').add({
        from_user_id: 'system',
        to_user_id: house.landlord_id,
        type: 'system',
        title: `房源审核${approved ? '通过' : '未通过'}`,
        content: approved ?
          `您的房源"${house.title}"已通过审核，现在可以正常展示了！` :
          `您的房源"${house.title}"审核未通过，原因：${reason}`,
        related_id: house._id,
        is_read: false,
        create_date: new Date()
      });
    });

    await messageBatch.commit();

    return {
      code: 0,
      message: `批量审核成功，处理了${houseIds.length}个房源`
    };
  } catch (error) {
    return {
      code: 500,
      message: '批量审核失败',
      error: error.message
    };
  }
}

// 获取房源统计信息
async function getHouseStatistics(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();

    // 总房源数
    const totalHouses = await db.collection('houses').count();

    // 已审核房源数
    const verifiedHouses = await db.collection('houses').where({
      is_verified: true
    }).count();

    // 待审核房源数
    const pendingHouses = await db.collection('houses').where({
      is_verified: false
    }).count();

    // 可租房源数
    const availableHouses = await db.collection('houses').where({
      status: 'available',
      is_verified: true
    }).count();

    // 已租房源数
    const rentedHouses = await db.collection('houses').where({
      status: 'rented'
    }).count();

    // 今日新增房源
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayHouses = await db.collection('houses').where({
      publish_date: db.command.gte(today)
    }).count();

    // 房源类型分布
    const typeStats = await db.collection('houses').aggregate()
      .group({
        _id: '$type',
        count: db.command.aggregate.sum(1),
        avgPrice: db.command.aggregate.avg('$price')
      })
      .end();

    // 地区分布
    const locationStats = await db.collection('houses').aggregate()
      .group({
        _id: '$location.district',
        count: db.command.aggregate.sum(1)
      })
      .sort({ count: -1 })
      .limit(10)
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalHouses.total,
        verified: verifiedHouses.total,
        pending: pendingHouses.total,
        available: availableHouses.total,
        rented: rentedHouses.total,
        todayNew: todayHouses.total,
        typeDistribution: typeStats.data,
        locationDistribution: locationStats.data
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取房源统计失败',
      error: error.message
    };
  }
}

// 删除房源
async function deleteHouse(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { houseId, reason = '' } = event.data;

  if (!houseId) {
    return {
      code: 400,
      message: '缺少房源ID'
    };
  }

  try {
    const db = uniCloud.database();

    // 获取房源信息
    const houseRes = await db.collection('houses').doc(houseId).get();
    if (houseRes.data.length === 0) {
      return {
        code: 404,
        message: '房源不存在'
      };
    }

    const house = houseRes.data[0];

    // 删除房源
    await db.collection('houses').doc(houseId).remove();

    // 删除相关收藏
    await db.collection('favorites').where({
      house_id: houseId
    }).remove();

    // 删除相关预约
    await db.collection('appointments').where({
      house_id: houseId
    }).remove();

    // 发送通知给房东
    await db.collection('messages').add({
      from_user_id: 'system',
      to_user_id: house.landlord_id,
      type: 'system',
      title: '房源已被删除',
      content: `您的房源"${house.title}"已被管理员删除。${reason ? '原因：' + reason : ''}`,
      is_read: false,
      create_date: new Date()
    });

    // 记录操作日志
    await db.collection('admin_logs').add({
      admin_id: adminCheck.uid,
      action: 'delete_house',
      target_id: houseId,
      details: {
        title: house.title,
        reason: reason
      },
      create_date: new Date()
    });

    return {
      code: 0,
      message: '房源删除成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '删除房源失败',
      error: error.message
    };
  }
}

// 预约管理相关函数
async function getAppointmentList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 20, status = null, startDate = null, endDate = null } = event.data || {};

  try {
    const db = uniCloud.database();
    let where = {};

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 日期范围筛选
    if (startDate && endDate) {
      where.appointment_date = db.command.and([
        db.command.gte(new Date(startDate)),
        db.command.lte(new Date(endDate))
      ]);
    }

    // 获取总数
    const countResult = await db.collection('appointments').where(where).count();
    const total = countResult.total;

    // 获取预约列表，关联用户和房源信息
    const appointmentResult = await db.collection('appointments')
      .aggregate()
      .match(where)
      .lookup({
        from: 'uni-id-users',
        localField: 'user_id',
        foreignField: '_id',
        as: 'user'
      })
      .lookup({
        from: 'houses',
        localField: 'house_id',
        foreignField: '_id',
        as: 'house'
      })
      .lookup({
        from: 'uni-id-users',
        localField: 'publisher_id',
        foreignField: '_id',
        as: 'publisher'
      })
      .project({
        appointment_date: 1,
        contact_phone: 1,
        message: 1,
        status: 1,
        create_date: 1,
        update_date: 1,
        'user.nickname': 1,
        'user.mobile': 1,
        'house.title': 1,
        'house.location': 1,
        'publisher.nickname': 1,
        'publisher.mobile': 1
      })
      .sort({ create_date: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: appointmentResult.data,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取预约列表失败',
      error: error.message
    };
  }
}

// 获取预约统计信息
async function getAppointmentStatistics(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();

    // 总预约数
    const totalAppointments = await db.collection('appointments').count();

    // 待确认预约数
    const pendingAppointments = await db.collection('appointments').where({
      status: 'pending'
    }).count();

    // 已确认预约数
    const confirmedAppointments = await db.collection('appointments').where({
      status: 'confirmed'
    }).count();

    // 已完成预约数
    const completedAppointments = await db.collection('appointments').where({
      status: 'completed'
    }).count();

    // 今日预约数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayAppointments = await db.collection('appointments').where({
      create_date: db.command.gte(today)
    }).count();

    // 预约状态分布
    const statusStats = await db.collection('appointments').aggregate()
      .group({
        _id: '$status',
        count: db.command.aggregate.sum(1)
      })
      .end();

    // 每日预约趋势（最近7天）
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const dailyStats = await db.collection('appointments').aggregate()
      .match({
        create_date: db.command.gte(weekAgo)
      })
      .project({
        date: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$create_date'
          }
        }
      })
      .group({
        _id: '$date',
        count: db.command.aggregate.sum(1)
      })
      .sort({ _id: 1 })
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalAppointments.total,
        pending: pendingAppointments.total,
        confirmed: confirmedAppointments.total,
        completed: completedAppointments.total,
        todayNew: todayAppointments.total,
        statusDistribution: statusStats.data,
        dailyTrend: dailyStats.data
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取预约统计失败',
      error: error.message
    };
  }
}

// 消息管理相关函数
async function getMessageList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 20, type = null, isRead = null } = event.data || {};

  try {
    const db = uniCloud.database();
    let where = {};

    // 类型筛选
    if (type) {
      where.type = type;
    }

    // 已读状态筛选
    if (isRead !== null) {
      where.is_read = isRead;
    }

    // 获取总数
    const countResult = await db.collection('messages').where(where).count();
    const total = countResult.total;

    // 获取消息列表，关联用户信息
    const messageResult = await db.collection('messages')
      .aggregate()
      .match(where)
      .lookup({
        from: 'uni-id-users',
        localField: 'from_user_id',
        foreignField: '_id',
        as: 'from_user'
      })
      .lookup({
        from: 'uni-id-users',
        localField: 'to_user_id',
        foreignField: '_id',
        as: 'to_user'
      })
      .project({
        type: 1,
        title: 1,
        content: 1,
        related_id: 1,
        is_read: 1,
        create_date: 1,
        'from_user.nickname': 1,
        'to_user.nickname': 1
      })
      .sort({ create_date: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: messageResult.data,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取消息列表失败',
      error: error.message
    };
  }
}

// 发送系统消息
async function sendSystemMessage(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { userIds, title, content, type = 'system' } = event.data;

  if (!title || !content) {
    return {
      code: 400,
      message: '标题和内容不能为空'
    };
  }

  try {
    const db = uniCloud.database();

    // 如果没有指定用户，发送给所有用户
    let targetUserIds = userIds;
    if (!userIds || userIds.length === 0) {
      const allUsersRes = await db.collection('uni-id-users').field({ _id: 1 }).get();
      targetUserIds = allUsersRes.data.map(user => user._id);
    }

    // 批量发送消息
    const batch = db.batch();
    targetUserIds.forEach(userId => {
      batch.collection('messages').add({
        from_user_id: 'system',
        to_user_id: userId,
        type: type,
        title: title,
        content: content,
        is_read: false,
        create_date: new Date()
      });
    });

    await batch.commit();

    return {
      code: 0,
      message: `系统消息发送成功，共发送给${targetUserIds.length}个用户`
    };
  } catch (error) {
    return {
      code: 500,
      message: '发送系统消息失败',
      error: error.message
    };
  }
}

// 批量发送消息
async function batchSendMessage(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { condition, title, content, type = 'system' } = event.data;

  if (!title || !content) {
    return {
      code: 400,
      message: '标题和内容不能为空'
    };
  }

  try {
    const db = uniCloud.database();

    // 根据条件查询用户
    let where = {};
    if (condition) {
      if (condition.role) {
        where.role = db.command.in([condition.role]);
      }
      if (condition.status !== undefined) {
        where.status = condition.status;
      }
      if (condition.verified !== undefined) {
        if (condition.verified === 'student') {
          where['student_info.verified'] = true;
        } else if (condition.verified === 'landlord') {
          where['landlord_info.verified'] = true;
        }
      }
    }

    const usersRes = await db.collection('uni-id-users').where(where).field({ _id: 1 }).get();
    const userIds = usersRes.data.map(user => user._id);

    if (userIds.length === 0) {
      return {
        code: 400,
        message: '没有找到符合条件的用户'
      };
    }

    // 批量发送消息
    const batch = db.batch();
    userIds.forEach(userId => {
      batch.collection('messages').add({
        from_user_id: 'system',
        to_user_id: userId,
        type: type,
        title: title,
        content: content,
        is_read: false,
        create_date: new Date()
      });
    });

    await batch.commit();

    return {
      code: 0,
      message: `批量消息发送成功，共发送给${userIds.length}个用户`
    };
  } catch (error) {
    return {
      code: 500,
      message: '批量发送消息失败',
      error: error.message
    };
  }
}

// 举报管理相关函数
async function getReportList(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { page = 1, pageSize = 20, status = null, targetType = null } = event.data || {};

  try {
    const db = uniCloud.database();
    let where = {};

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 举报目标类型筛选
    if (targetType) {
      where.target_type = targetType;
    }

    // 获取总数
    const countResult = await db.collection('reports').where(where).count();
    const total = countResult.total;

    // 获取举报列表，关联相关信息
    const reportResult = await db.collection('reports')
      .aggregate()
      .match(where)
      .lookup({
        from: 'uni-id-users',
        localField: 'reporter_id',
        foreignField: '_id',
        as: 'reporter'
      })
      .lookup({
        from: 'uni-id-users',
        localField: 'processor_id',
        foreignField: '_id',
        as: 'processor'
      })
      .project({
        target_type: 1,
        target_id: 1,
        reason: 1,
        description: 1,
        status: 1,
        create_date: 1,
        process_date: 1,
        'reporter.nickname': 1,
        'processor.nickname': 1
      })
      .sort({ create_date: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: reportResult.data,
        total: total,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取举报列表失败',
      error: error.message
    };
  }
}

// 处理举报
async function handleReport(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { reportId, action, reason = '' } = event.data;

  if (!reportId || !action) {
    return {
      code: 400,
      message: '缺少必要参数'
    };
  }

  try {
    const db = uniCloud.database();

    // 获取举报信息
    const reportRes = await db.collection('reports').doc(reportId).get();
    if (reportRes.data.length === 0) {
      return {
        code: 404,
        message: '举报记录不存在'
      };
    }

    const report = reportRes.data[0];

    // 更新举报状态
    await db.collection('reports').doc(reportId).update({
      status: action === 'approve' ? 'processed' : 'rejected',
      process_date: new Date(),
      processor_id: adminCheck.uid,
      process_reason: reason
    });

    // 如果举报被批准，需要对被举报对象进行处理
    if (action === 'approve') {
      if (report.target_type === 'house') {
        // 下架房源
        await db.collection('houses').doc(report.target_id).update({
          status: 'offline',
          offline_reason: '违规被举报'
        });

        // 通知房东
        const houseRes = await db.collection('houses').doc(report.target_id).get();
        if (houseRes.data.length > 0) {
          await db.collection('messages').add({
            from_user_id: 'system',
            to_user_id: houseRes.data[0].landlord_id,
            type: 'system',
            title: '房源被举报下架',
            content: `您的房源"${houseRes.data[0].title}"因违规被举报已下架处理。`,
            is_read: false,
            create_date: new Date()
          });
        }
      } else if (report.target_type === 'user') {
        // 禁用用户
        await db.collection('uni-id-users').doc(report.target_id).update({
          status: 1,
          ban_reason: '违规被举报'
        });

        // 通知用户
        await db.collection('messages').add({
          from_user_id: 'system',
          to_user_id: report.target_id,
          type: 'system',
          title: '账户被举报禁用',
          content: '您的账户因违规被举报已被禁用，如有疑问请联系客服。',
          is_read: false,
          create_date: new Date()
        });
      }
    }

    // 通知举报人
    await db.collection('messages').add({
      from_user_id: 'system',
      to_user_id: report.reporter_id,
      type: 'system',
      title: '举报处理结果',
      content: action === 'approve' ?
        '您的举报已被受理，感谢您的监督！' :
        `您的举报经审核不成立。${reason ? '原因：' + reason : ''}`,
      is_read: false,
      create_date: new Date()
    });

    return {
      code: 0,
      message: '举报处理成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '处理举报失败',
      error: error.message
    };
  }
}

// 获取仪表板数据
async function getDashboardData(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();

    // 并发获取各种统计数据
    const [
      totalUsers,
      totalHouses,
      totalAppointments,
      totalMessages,
      pendingReports,
      todayUsers,
      todayHouses,
      todayAppointments
    ] = await Promise.all([
      db.collection('uni-id-users').count(),
      db.collection('houses').count(),
      db.collection('appointments').count(),
      db.collection('messages').count(),
      db.collection('reports').where({ status: 'pending' }).count(),
      db.collection('uni-id-users').where({
        register_date: db.command.gte(getTodayStart())
      }).count(),
      db.collection('houses').where({
        publish_date: db.command.gte(getTodayStart())
      }).count(),
      db.collection('appointments').where({
        create_date: db.command.gte(getTodayStart())
      }).count()
    ]);

    // 获取最近7天的数据趋势
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const dailyStats = await db.collection('uni-id-users').aggregate()
      .match({
        register_date: db.command.gte(weekAgo)
      })
      .project({
        date: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$register_date'
          }
        }
      })
      .group({
        _id: '$date',
        count: db.command.aggregate.sum(1)
      })
      .sort({ _id: 1 })
      .end();

    return {
      code: 0,
      message: '获取成功',
      data: {
        overview: {
          totalUsers: totalUsers.total,
          totalHouses: totalHouses.total,
          totalAppointments: totalAppointments.total,
          totalMessages: totalMessages.total,
          pendingReports: pendingReports.total
        },
        today: {
          newUsers: todayUsers.total,
          newHouses: todayHouses.total,
          newAppointments: todayAppointments.total
        },
        trend: {
          userRegistration: dailyStats.data
        }
      }
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取仪表板数据失败',
      error: error.message
    };
  }
}

// 获取详细统计数据
async function getDetailedStatistics(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { type, startDate, endDate } = event.data || {};

  try {
    const db = uniCloud.database();
    let result = {};

    // 构建日期查询条件
    let dateQuery = {};
    if (startDate && endDate) {
      dateQuery = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    switch (type) {
      case 'user':
        // 用户相关统计
        result = await getUserDetailedStats(db, dateQuery);
        break;
      case 'house':
        // 房源相关统计
        result = await getHouseDetailedStats(db, dateQuery);
        break;
      case 'appointment':
        // 预约相关统计
        result = await getAppointmentDetailedStats(db, dateQuery);
        break;
      default:
        return {
          code: 400,
          message: '无效的统计类型'
        };
    }

    return {
      code: 0,
      message: '获取成功',
      data: result
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取详细统计失败',
      error: error.message
    };
  }
}

// 获取系统配置
async function getSystemConfig(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  try {
    const db = uniCloud.database();
    const configRes = await db.collection('system_config').get();

    const config = {};
    configRes.data.forEach(item => {
      config[item.key] = item.value;
    });

    return {
      code: 0,
      message: '获取成功',
      data: config
    };
  } catch (error) {
    return {
      code: 500,
      message: '获取系统配置失败',
      error: error.message
    };
  }
}

// 更新系统配置
async function updateSystemConfig(event, context) {
  const adminCheck = await checkAdminPermission(event.uniIdToken, context);
  if (adminCheck.code !== 0) {
    return adminCheck;
  }

  const { configs } = event.data;

  if (!configs || typeof configs !== 'object') {
    return {
      code: 400,
      message: '配置数据格式错误'
    };
  }

  try {
    const db = uniCloud.database();

    // 批量更新配置
    const promises = Object.keys(configs).map(key => {
      return db.collection('system_config').where({
        key: key
      }).update({
        value: configs[key],
        update_date: new Date()
      });
    });

    await Promise.all(promises);

    return {
      code: 0,
      message: '配置更新成功'
    };
  } catch (error) {
    return {
      code: 500,
      message: '更新配置失败',
      error: error.message
    };
  }
}

// 辅助函数
function getTodayStart() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
}

async function getUserDetailedStats(db, dateQuery) {
  // 用户详细统计实现
  return {};
}

async function getHouseDetailedStats(db, dateQuery) {
  // 房源详细统计实现
  return {};
}

async function getAppointmentDetailedStats(db, dateQuery) {
  // 预约详细统计实现
  return {};
}
