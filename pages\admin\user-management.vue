<template>
  <view class="admin-user-management">
    <!-- 页面标题 -->
    <uni-card title="用户管理" is-full>
      <!-- 搜索和筛选 -->
      <view class="search-section">
        <uni-row :gutter="20">
          <uni-col :span="6">
            <uni-easyinput 
              v-model="searchForm.keyword" 
              placeholder="搜索用户名/手机号/邮箱"
              @confirm="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.status"
              :localdata="statusOptions"
              placeholder="用户状态"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.role"
              :localdata="roleOptions"
              placeholder="用户角色"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <button class="uni-button" type="primary" @click="handleSearch">搜索</button>
          </uni-col>
          <uni-col :span="6">
            <button class="uni-button" @click="resetSearch">重置</button>
            <button class="uni-button" type="primary" @click="showBatchModal = true">批量操作</button>
          </uni-col>
        </uni-row>
      </view>

      <!-- 用户列表 -->
      <uni-table 
        ref="table" 
        :loading="loading" 
        border 
        stripe 
        type="selection" 
        @selection-change="handleSelectionChange"
      >
        <uni-tr>
          <uni-th align="center">用户名</uni-th>
          <uni-th align="center">昵称</uni-th>
          <uni-th align="center">手机号</uni-th>
          <uni-th align="center">角色</uni-th>
          <uni-th align="center">状态</uni-th>
          <uni-th align="center">注册时间</uni-th>
          <uni-th align="center">操作</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in userList" :key="index">
          <uni-td align="center">{{ item.username || '-' }}</uni-td>
          <uni-td align="center">{{ item.nickname || '-' }}</uni-td>
          <uni-td align="center">{{ item.mobile || '-' }}</uni-td>
          <uni-td align="center">
            <uni-tag 
              v-for="role in item.role" 
              :key="role" 
              :text="getRoleText(role)"
              type="primary"
              size="small"
            />
          </uni-td>
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(item.status)"
              :type="getStatusType(item.status)"
            />
          </uni-td>
          <uni-td align="center">{{ formatDate(item.register_date) }}</uni-td>
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="uni-button uni-button--mini" 
                type="primary" 
                @click="viewUser(item)"
              >
                查看
              </button>
              <button 
                class="uni-button uni-button--mini" 
                :type="item.status === 0 ? 'warn' : 'default'"
                @click="toggleUserStatus(item)"
              >
                {{ item.status === 0 ? '禁用' : '启用' }}
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="primary"
                @click="verifyIdentity(item)"
                v-if="needVerification(item)"
              >
                身份认证
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>

      <!-- 分页 -->
      <view class="pagination-section">
        <uni-pagination 
          :current="pagination.page"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @change="handlePageChange"
        />
      </view>
    </uni-card>

    <!-- 批量操作弹窗 -->
    <uni-popup ref="batchPopup" v-model="showBatchModal" type="dialog">
      <uni-popup-dialog 
        title="批量操作"
        @confirm="handleBatchOperation"
        @close="showBatchModal = false"
      >
        <view class="batch-form">
          <uni-forms ref="batchForm" :model="batchForm" :rules="batchRules">
            <uni-forms-item label="操作类型" name="operation" required>
              <uni-data-select
                v-model="batchForm.operation"
                :localdata="batchOperations"
                placeholder="请选择操作类型"
              />
            </uni-forms-item>
            <uni-forms-item 
              v-if="batchForm.operation === 'updateStatus'" 
              label="状态" 
              name="status" 
              required
            >
              <uni-data-select
                v-model="batchForm.status"
                :localdata="statusOptions"
                placeholder="请选择状态"
              />
            </uni-forms-item>
            <uni-forms-item 
              v-if="batchForm.operation === 'addRole' || batchForm.operation === 'removeRole'" 
              label="角色" 
              name="role" 
              required
            >
              <uni-data-select
                v-model="batchForm.role"
                :localdata="roleOptions"
                placeholder="请选择角色"
              />
            </uni-forms-item>
          </uni-forms>
          <view class="selected-info">
            已选择 {{ selectedUsers.length }} 个用户
          </view>
        </view>
      </uni-popup-dialog>
    </uni-popup>

    <!-- 用户详情弹窗 -->
    <uni-popup ref="userDetailPopup" v-model="showUserDetail" type="dialog">
      <uni-popup-dialog 
        title="用户详情"
        @close="showUserDetail = false"
      >
        <view class="user-detail" v-if="currentUser">
          <uni-forms :model="currentUser" label-width="100px">
            <uni-forms-item label="用户名">{{ currentUser.username || '-' }}</uni-forms-item>
            <uni-forms-item label="昵称">{{ currentUser.nickname || '-' }}</uni-forms-item>
            <uni-forms-item label="手机号">{{ currentUser.mobile || '-' }}</uni-forms-item>
            <uni-forms-item label="邮箱">{{ currentUser.email || '-' }}</uni-forms-item>
            <uni-forms-item label="角色">
              <uni-tag 
                v-for="role in currentUser.role" 
                :key="role" 
                :text="getRoleText(role)"
                type="primary"
                size="small"
              />
            </uni-forms-item>
            <uni-forms-item label="状态">
              <uni-tag 
                :text="getStatusText(currentUser.status)"
                :type="getStatusType(currentUser.status)"
              />
            </uni-forms-item>
            <uni-forms-item label="注册时间">{{ formatDate(currentUser.register_date) }}</uni-forms-item>
            <uni-forms-item label="最后登录">{{ formatDate(currentUser.last_login_date) }}</uni-forms-item>
            
            <!-- 学生信息 -->
            <template v-if="currentUser.student_info">
              <uni-forms-item label="学校">{{ currentUser.student_info.school || '-' }}</uni-forms-item>
              <uni-forms-item label="学号">{{ currentUser.student_info.student_id || '-' }}</uni-forms-item>
              <uni-forms-item label="学生认证">
                <uni-tag 
                  :text="currentUser.student_info.verified ? '已认证' : '未认证'"
                  :type="currentUser.student_info.verified ? 'success' : 'default'"
                />
              </uni-forms-item>
            </template>
            
            <!-- 房东信息 -->
            <template v-if="currentUser.landlord_info">
              <uni-forms-item label="真实姓名">{{ currentUser.landlord_info.real_name || '-' }}</uni-forms-item>
              <uni-forms-item label="身份证号">{{ currentUser.landlord_info.id_card || '-' }}</uni-forms-item>
              <uni-forms-item label="房东认证">
                <uni-tag 
                  :text="currentUser.landlord_info.verified ? '已认证' : '未认证'"
                  :type="currentUser.landlord_info.verified ? 'success' : 'default'"
                />
              </uni-forms-item>
            </template>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      userList: [],
      selectedUsers: [],
      showBatchModal: false,
      showUserDetail: false,
      currentUser: null,
      
      // 搜索表单
      searchForm: {
        keyword: '',
        status: null,
        role: null
      },
      
      // 分页
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      },
      
      // 批量操作表单
      batchForm: {
        operation: '',
        status: null,
        role: ''
      },
      
      // 选项数据
      statusOptions: [
        { value: null, text: '全部状态' },
        { value: 0, text: '正常' },
        { value: 1, text: '禁用' },
        { value: 2, text: '审核中' }
      ],
      
      roleOptions: [
        { value: null, text: '全部角色' },
        { value: 'student', text: '学生' },
        { value: 'landlord', text: '房东' },
        { value: 'admin', text: '管理员' }
      ],
      
      batchOperations: [
        { value: 'updateStatus', text: '更新状态' },
        { value: 'addRole', text: '添加角色' },
        { value: 'removeRole', text: '移除角色' }
      ],
      
      // 表单验证规则
      batchRules: {
        operation: {
          rules: [{ required: true, errorMessage: '请选择操作类型' }]
        }
      }
    }
  },
  
  onLoad() {
    this.loadUserList();
  },
  
  methods: {
    // 加载用户列表
    async loadUserList() {
      this.loading = true;
      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'getUserList',
            data: {
              ...this.searchForm,
              page: this.pagination.page,
              pageSize: this.pagination.pageSize
            }
          }
        });

        if (res.result.code === 0) {
          this.userList = res.result.data.list;
          this.pagination.total = res.result.data.total;
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('加载用户列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.loadUserList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        status: null,
        role: null
      };
      this.handleSearch();
    },

    // 分页变化
    handlePageChange(e) {
      this.pagination.page = e.current;
      this.loadUserList();
    },

    // 选择变化
    handleSelectionChange(e) {
      this.selectedUsers = e.detail.value;
    },

    // 查看用户详情
    viewUser(user) {
      this.currentUser = user;
      this.showUserDetail = true;
    },

    // 切换用户状态
    async toggleUserStatus(user) {
      const newStatus = user.status === 0 ? 1 : 0;
      const statusText = newStatus === 0 ? '启用' : '禁用';

      const confirmResult = await uni.showModal({
        title: '确认操作',
        content: `确定要${statusText}用户"${user.nickname || user.username}"吗？`
      });

      if (!confirmResult.confirm) return;

      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'updateUserStatus',
            data: {
              userId: user._id,
              status: newStatus
            }
          }
        });

        if (res.result.code === 0) {
          uni.showToast({
            title: '操作成功',
            icon: 'success'
          });
          this.loadUserList();
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('更新用户状态失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        });
      }
    },

    // 身份认证
    async verifyIdentity(user) {
      const identityType = user.student_info && !user.student_info.verified ? 'student' : 'landlord';

      uni.navigateTo({
        url: `/pages/admin/identity-verify?userId=${user._id}&type=${identityType}`
      });
    },

    // 批量操作
    async handleBatchOperation() {
      if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: '请先选择用户',
          icon: 'none'
        });
        return;
      }

      try {
        await this.$refs.batchForm.validate();

        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'batchUpdateUsers',
            data: {
              userIds: this.selectedUsers.map(user => user._id),
              operation: this.batchForm.operation,
              data: {
                status: this.batchForm.status,
                role: this.batchForm.role
              }
            }
          }
        });

        if (res.result.code === 0) {
          uni.showToast({
            title: '批量操作成功',
            icon: 'success'
          });
          this.showBatchModal = false;
          this.loadUserList();
          this.$refs.table.clearSelection();
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('批量操作失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        });
      }
    },

    // 辅助方法
    getRoleText(role) {
      const roleMap = {
        student: '学生',
        landlord: '房东',
        admin: '管理员',
        super_admin: '超级管理员'
      };
      return roleMap[role] || role;
    },

    getStatusText(status) {
      const statusMap = {
        0: '正常',
        1: '禁用',
        2: '审核中'
      };
      return statusMap[status] || '未知';
    },

    getStatusType(status) {
      const typeMap = {
        0: 'success',
        1: 'error',
        2: 'warning'
      };
      return typeMap[status] || 'default';
    },

    needVerification(user) {
      return (user.student_info && !user.student_info.verified) ||
             (user.landlord_info && !user.landlord_info.verified);
    },

    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.admin-user-management {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.batch-form {
  padding: 20px;
}

.selected-info {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.user-detail {
  max-height: 400px;
  overflow-y: auto;
}
</style>
