<template>
  <view class="admin-house-management">
    <!-- 页面标题 -->
    <uni-card title="房源管理" is-full>
      <!-- 搜索和筛选 -->
      <view class="search-section">
        <uni-row :gutter="20">
          <uni-col :span="6">
            <uni-easyinput 
              v-model="searchForm.keyword" 
              placeholder="搜索房源标题/地址"
              @confirm="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.status"
              :localdata="statusOptions"
              placeholder="房源状态"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <uni-data-select
              v-model="searchForm.isVerified"
              :localdata="verifyOptions"
              placeholder="审核状态"
              @change="handleSearch"
            />
          </uni-col>
          <uni-col :span="4">
            <button class="uni-button" type="primary" @click="handleSearch">搜索</button>
          </uni-col>
          <uni-col :span="6">
            <button class="uni-button" @click="resetSearch">重置</button>
            <button class="uni-button" type="primary" @click="showBatchAuditModal = true">批量审核</button>
          </uni-col>
        </uni-row>
      </view>

      <!-- 房源列表 -->
      <uni-table 
        ref="table" 
        :loading="loading" 
        border 
        stripe 
        type="selection" 
        @selection-change="handleSelectionChange"
      >
        <uni-tr>
          <uni-th align="center">房源标题</uni-th>
          <uni-th align="center">类型</uni-th>
          <uni-th align="center">价格</uni-th>
          <uni-th align="center">地址</uni-th>
          <uni-th align="center">房东</uni-th>
          <uni-th align="center">状态</uni-th>
          <uni-th align="center">审核状态</uni-th>
          <uni-th align="center">发布时间</uni-th>
          <uni-th align="center">操作</uni-th>
        </uni-tr>
        <uni-tr v-for="(item, index) in houseList" :key="index">
          <uni-td align="center">
            <view class="house-title" @click="viewHouse(item)">
              {{ item.title }}
            </view>
          </uni-td>
          <uni-td align="center">{{ getTypeText(item.type) }}</uni-td>
          <uni-td align="center">¥{{ item.price }}/月</uni-td>
          <uni-td align="center">{{ item.location?.address || '-' }}</uni-td>
          <uni-td align="center">{{ item.landlord?.[0]?.nickname || '-' }}</uni-td>
          <uni-td align="center">
            <uni-tag 
              :text="getStatusText(item.status)"
              :type="getStatusType(item.status)"
            />
          </uni-td>
          <uni-td align="center">
            <uni-tag 
              :text="item.is_verified ? '已审核' : '待审核'"
              :type="item.is_verified ? 'success' : 'warning'"
            />
          </uni-td>
          <uni-td align="center">{{ formatDate(item.publish_date) }}</uni-td>
          <uni-td align="center">
            <view class="action-buttons">
              <button 
                class="uni-button uni-button--mini" 
                type="primary" 
                @click="viewHouse(item)"
              >
                查看
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="primary"
                @click="auditHouse(item, true)"
                v-if="!item.is_verified"
              >
                通过
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="warn"
                @click="auditHouse(item, false)"
                v-if="!item.is_verified"
              >
                拒绝
              </button>
              <button 
                class="uni-button uni-button--mini" 
                type="error"
                @click="deleteHouse(item)"
              >
                删除
              </button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>

      <!-- 分页 -->
      <view class="pagination-section">
        <uni-pagination 
          :current="pagination.page"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @change="handlePageChange"
        />
      </view>
    </uni-card>

    <!-- 批量审核弹窗 -->
    <uni-popup ref="batchAuditPopup" v-model="showBatchAuditModal" type="dialog">
      <uni-popup-dialog 
        title="批量审核"
        @confirm="handleBatchAudit"
        @close="showBatchAuditModal = false"
      >
        <view class="batch-form">
          <uni-forms ref="batchForm" :model="batchForm" :rules="batchRules">
            <uni-forms-item label="审核结果" name="approved" required>
              <uni-data-select
                v-model="batchForm.approved"
                :localdata="auditOptions"
                placeholder="请选择审核结果"
              />
            </uni-forms-item>
            <uni-forms-item label="审核原因" name="reason">
              <uni-easyinput 
                v-model="batchForm.reason" 
                type="textarea"
                placeholder="请输入审核原因（拒绝时必填）"
                :maxlength="200"
              />
            </uni-forms-item>
          </uni-forms>
          <view class="selected-info">
            已选择 {{ selectedHouses.length }} 个房源
          </view>
        </view>
      </uni-popup-dialog>
    </uni-popup>

    <!-- 房源详情弹窗 -->
    <uni-popup ref="houseDetailPopup" v-model="showHouseDetail" type="dialog">
      <uni-popup-dialog 
        title="房源详情"
        @close="showHouseDetail = false"
      >
        <view class="house-detail" v-if="currentHouse">
          <uni-forms :model="currentHouse" label-width="100px">
            <uni-forms-item label="标题">{{ currentHouse.title }}</uni-forms-item>
            <uni-forms-item label="类型">{{ getTypeText(currentHouse.type) }}</uni-forms-item>
            <uni-forms-item label="价格">¥{{ currentHouse.price }}/月</uni-forms-item>
            <uni-forms-item label="面积">{{ currentHouse.area }}㎡</uni-forms-item>
            <uni-forms-item label="地址">{{ currentHouse.location?.address }}</uni-forms-item>
            <uni-forms-item label="详细描述">
              <view class="description">{{ currentHouse.description }}</view>
            </uni-forms-item>
            <uni-forms-item label="房源图片" v-if="currentHouse.images && currentHouse.images.length">
              <view class="image-list">
                <image 
                  v-for="(img, index) in currentHouse.images" 
                  :key="index"
                  :src="img"
                  class="house-image"
                  mode="aspectFill"
                  @click="previewImage(img, currentHouse.images)"
                />
              </view>
            </uni-forms-item>
            <uni-forms-item label="状态">
              <uni-tag 
                :text="getStatusText(currentHouse.status)"
                :type="getStatusType(currentHouse.status)"
              />
            </uni-forms-item>
            <uni-forms-item label="审核状态">
              <uni-tag 
                :text="currentHouse.is_verified ? '已审核' : '待审核'"
                :type="currentHouse.is_verified ? 'success' : 'warning'"
              />
            </uni-forms-item>
            <uni-forms-item label="浏览次数">{{ currentHouse.view_count || 0 }}</uni-forms-item>
            <uni-forms-item label="收藏次数">{{ currentHouse.favorite_count || 0 }}</uni-forms-item>
            <uni-forms-item label="发布时间">{{ formatDate(currentHouse.publish_date) }}</uni-forms-item>
          </uni-forms>
        </view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      houseList: [],
      selectedHouses: [],
      showBatchAuditModal: false,
      showHouseDetail: false,
      currentHouse: null,
      
      // 搜索表单
      searchForm: {
        keyword: '',
        status: null,
        isVerified: null
      },
      
      // 分页
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      },
      
      // 批量审核表单
      batchForm: {
        approved: null,
        reason: ''
      },
      
      // 选项数据
      statusOptions: [
        { value: null, text: '全部状态' },
        { value: 'available', text: '可租' },
        { value: 'rented', text: '已租' },
        { value: 'offline', text: '下架' }
      ],
      
      verifyOptions: [
        { value: null, text: '全部' },
        { value: true, text: '已审核' },
        { value: false, text: '待审核' }
      ],
      
      auditOptions: [
        { value: true, text: '通过' },
        { value: false, text: '拒绝' }
      ],
      
      // 表单验证规则
      batchRules: {
        approved: {
          rules: [{ required: true, errorMessage: '请选择审核结果' }]
        }
      }
    }
  },
  
  onLoad() {
    this.loadHouseList();
  },
  
  methods: {
    // 加载房源列表
    async loadHouseList() {
      this.loading = true;
      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'getHouseList',
            data: {
              ...this.searchForm,
              page: this.pagination.page,
              pageSize: this.pagination.pageSize
            }
          }
        });

        if (res.result.code === 0) {
          this.houseList = res.result.data.list;
          this.pagination.total = res.result.data.total;
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('加载房源列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.loadHouseList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        status: null,
        isVerified: null
      };
      this.handleSearch();
    },

    // 分页变化
    handlePageChange(e) {
      this.pagination.page = e.current;
      this.loadHouseList();
    },

    // 选择变化
    handleSelectionChange(e) {
      this.selectedHouses = e.detail.value;
    },

    // 查看房源详情
    viewHouse(house) {
      this.currentHouse = house;
      this.showHouseDetail = true;
    },

    // 审核房源
    async auditHouse(house, approved) {
      let reason = '';

      if (!approved) {
        const inputResult = await uni.showModal({
          title: '拒绝原因',
          content: '请输入拒绝原因',
          editable: true,
          placeholderText: '请输入拒绝原因'
        });

        if (!inputResult.confirm) return;
        reason = inputResult.content;

        if (!reason.trim()) {
          uni.showToast({
            title: '请输入拒绝原因',
            icon: 'none'
          });
          return;
        }
      }

      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'auditHouse',
            data: {
              houseId: house._id,
              approved: approved,
              reason: reason
            }
          }
        });

        if (res.result.code === 0) {
          uni.showToast({
            title: '审核成功',
            icon: 'success'
          });
          this.loadHouseList();
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('审核房源失败:', error);
        uni.showToast({
          title: '审核失败',
          icon: 'error'
        });
      }
    },

    // 删除房源
    async deleteHouse(house) {
      const confirmResult = await uni.showModal({
        title: '确认删除',
        content: `确定要删除房源"${house.title}"吗？此操作不可恢复！`
      });

      if (!confirmResult.confirm) return;

      let reason = '';
      const reasonResult = await uni.showModal({
        title: '删除原因',
        content: '请输入删除原因',
        editable: true,
        placeholderText: '请输入删除原因'
      });

      if (reasonResult.confirm) {
        reason = reasonResult.content;
      }

      try {
        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'deleteHouse',
            data: {
              houseId: house._id,
              reason: reason
            }
          }
        });

        if (res.result.code === 0) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          this.loadHouseList();
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('删除房源失败:', error);
        uni.showToast({
          title: '删除失败',
          icon: 'error'
        });
      }
    },

    // 批量审核
    async handleBatchAudit() {
      if (this.selectedHouses.length === 0) {
        uni.showToast({
          title: '请先选择房源',
          icon: 'none'
        });
        return;
      }

      if (!this.batchForm.approved && !this.batchForm.reason.trim()) {
        uni.showToast({
          title: '拒绝时必须填写原因',
          icon: 'none'
        });
        return;
      }

      try {
        await this.$refs.batchForm.validate();

        const res = await uniCloud.callFunction({
          name: 'admin-management',
          data: {
            action: 'batchAuditHouses',
            data: {
              houseIds: this.selectedHouses.map(house => house._id),
              approved: this.batchForm.approved,
              reason: this.batchForm.reason
            }
          }
        });

        if (res.result.code === 0) {
          uni.showToast({
            title: '批量审核成功',
            icon: 'success'
          });
          this.showBatchAuditModal = false;
          this.loadHouseList();
          this.$refs.table.clearSelection();
        } else {
          uni.showToast({
            title: res.result.message,
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('批量审核失败:', error);
        uni.showToast({
          title: '审核失败',
          icon: 'error'
        });
      }
    },

    // 预览图片
    previewImage(current, urls) {
      uni.previewImage({
        current: current,
        urls: urls
      });
    },

    // 辅助方法
    getTypeText(type) {
      const typeMap = {
        single: '单间',
        shared: '合租',
        whole: '整租',
        apartment: '公寓'
      };
      return typeMap[type] || type;
    },

    getStatusText(status) {
      const statusMap = {
        available: '可租',
        rented: '已租',
        offline: '下架'
      };
      return statusMap[status] || status;
    },

    getStatusType(status) {
      const typeMap = {
        available: 'success',
        rented: 'warning',
        offline: 'error'
      };
      return typeMap[status] || 'default';
    },

    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.admin-house-management {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.house-title {
  color: #007aff;
  cursor: pointer;
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.batch-form {
  padding: 20px;
}

.selected-info {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.house-detail {
  max-height: 500px;
  overflow-y: auto;
}

.description {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.house-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
